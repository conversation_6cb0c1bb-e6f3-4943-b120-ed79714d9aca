import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';

import '../../../common/model/analyze_details_model/alarms_statistic.dart';
import '../../../common/model/analyze_model.dart';
import '../../../common/widget/corner_card.dart';
import '../../analyze/controller.dart';
import '../controller.dart';

class ChainChart extends StatelessWidget {
  const ChainChart({super.key, required this.detail, required this.controller});

  final AnalyzeModel detail;
  final AnalyzedetailController controller;

  @override
  Widget build(BuildContext context) {
    List<AlarmsStatistic> datas = controller.details.alarmsStatistics!;
    List<AlarmsStatistic> chain = controller.chain.value.alarmsStatistics!;

    return CornerCard(
      height: 300,
      child: Column(
        children: [
          Text(
            '${_getYearMonth(detail)}警情环比',
            style: TextStyle(fontSize: 25, color: Colors.white),
          ),
          Expanded(
            child: charts.BarChart(
              _createSeriesList(datas, chain),
              animate: true,

              domainAxis: charts.OrdinalAxisSpec(
                renderSpec: charts.SmallTickRendererSpec(
                  labelStyle: charts.TextStyleSpec(
                      color: charts.Color.fromHex(code: '#ffffff')),
                ),
              ),
              // 自定义柱形渲染器来控制宽度
              defaultRenderer: charts.BarRendererConfig(
                // 设置柱形组的宽度比例 (0.0 到 1.0)
                groupingType: charts.BarGroupingType.grouped,
                // 柱形组内柱形之间的间距
                barGroupInnerPaddingPx: 4,
                // 自定义柱形宽度 - 通过设置最大柱形宽度
                // maxBarWidthPx: 40, // 每个柱形最大宽度
                // 显示inside标签
                barRendererDecorator: charts.BarLabelDecorator<String>(
                  labelAnchor: charts.BarLabelAnchor.middle,
                  labelPosition: charts.BarLabelPosition.outside,
                  outsideLabelStyleSpec: charts.TextStyleSpec(
                    color: charts.Color.fromHex(code: '#ffffff'),
                    fontSize: 12,
                  ),
                ),
              ),
              // 添加自定义渲染器用于百分比标签
              customSeriesRenderers: [
                charts.BarRendererConfig<String>(
                  customRendererId: 'percentageRenderer',
                  groupingType: charts.BarGroupingType.grouped,
                  barRendererDecorator: charts.BarLabelDecorator<String>(
                    labelAnchor: charts.BarLabelAnchor.middle,
                    labelPosition: charts.BarLabelPosition.outside,
                  ),
                ),
              ],
              behaviors: [
                // 添加图例
                charts.SeriesLegend(
                  position: charts.BehaviorPosition.top,
                  horizontalFirst: false,
                  desiredMaxRows: 1,
                  cellPadding: const EdgeInsets.only(right: 4.0, bottom: 4.0),
                  entryTextStyle: charts.TextStyleSpec(
                    color: charts.Color.fromHex(code: '#ffffff'),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 创建自定义宽度的柱形图系列
  List<charts.Series<AlarmsStatistic, String>> _createSeriesList(
      List<AlarmsStatistic> datas, List<AlarmsStatistic> chain) {
    // 计算本周期比上周期的百分比变化逻辑
    Map<String, double> percentageChanges =
        _calculatePercentageChanges(datas, chain);

    return [
      // 上周期系列 - 显示inside标签
      charts.Series<AlarmsStatistic, String>(
        id: '上周期',
        domainFn: (AlarmsStatistic data, _) => data.name ?? '',
        measureFn: (AlarmsStatistic data, _) => data.alarmCount,
        data: chain,
        labelAccessorFn: (AlarmsStatistic datum, int? index) =>
            '${datum.alarmCount}',
        colorFn: (_, __) => charts.MaterialPalette.cyan.shadeDefault,
        strokeWidthPxFn: (_, __) => 1.0,
      )..setAttribute(charts.rendererIdKey, 'default'),

      // 百分比变化系列 - 显示outside标签（透明柱子，只显示标签）
      // charts.Series<AlarmsStatistic, String>(
      //   id: '',
      //   domainFn: (AlarmsStatistic data, _) => data.name ?? '',
      //   measureFn: (AlarmsStatistic data, _) => data.alarmCount,
      //   data: datas,
      //   labelAccessorFn: (AlarmsStatistic datum, int? index) {
      //     String name = datum.name ?? '';
      //     double percentage = percentageChanges[name] ?? 0.0;
      //     if (percentage == 0) return '';
      //     String sign = percentage > 0 ? '↑' : '↓';
      //     return '$sign${percentage.toStringAsFixed(0)}%';
      //   },
      //   outsideLabelStyleAccessorFn: (datum, index) {
      //     String name = datum.name ?? '';
      //     double percentage = percentageChanges[name] ?? 0.0;

      //     return charts.TextStyleSpec(
      //       color: percentage > 0
      //           ? charts.Color.fromHex(code: '#ff0000')
      //           : percentage == 0
      //               ? charts.Color.fromHex(code: '#ffffff')
      //               : charts.Color.fromHex(code: '#00ff00'),
      //       fontSize: 15,
      //     );
      //   },
      //   // 设置为透明色，只显示标签，不在图例中显示
      //   colorFn: (_, __) => charts.Color.transparent,
      //   strokeWidthPxFn: (_, __) => 1.0,
      //   // displayName: null, // 不在图例中显示
      // )..setAttribute(charts.rendererIdKey, 'percentageRenderer'),

      charts.Series<AlarmsStatistic, String>(
        id: '',
        domainFn: (AlarmsStatistic data, _) => data.name ?? '',
        measureFn: (AlarmsStatistic data, _) => data.alarmCount,
        data: datas,
        labelAccessorFn: (AlarmsStatistic datum, int? index) {
          String name = datum.name ?? '';
          double percentage = percentageChanges[name] ?? 0.0;
          if (percentage == 0) return '';
          String sign = percentage > 0 ? '↑' : '↓';
          return '$sign${percentage.toStringAsFixed(0)}%';
        },
        outsideLabelStyleAccessorFn: (datum, index) {
          String name = datum.name ?? '';
          double percentage = percentageChanges[name] ?? 0.0;

          return charts.TextStyleSpec(
            color: percentage > 0
                ? charts.Color.fromHex(code: '#ff0000')
                : percentage == 0
                    ? charts.Color.fromHex(code: '#ffffff')
                    : charts.Color.fromHex(code: '#00ff00'),
            fontSize: 15,
          );
        },
        colorFn: (_, __) => charts.Color.transparent,
        strokeWidthPxFn: (_, __) => 1.0,
      )..setAttribute(charts.rendererIdKey, 'percentageRenderer'),

      // 本周期系列 - 显示inside标签（数值）
      charts.Series<AlarmsStatistic, String>(
        id: '本周期',
        domainFn: (AlarmsStatistic data, _) => data.name ?? '',
        measureFn: (AlarmsStatistic data, _) => data.alarmCount,
        data: datas,
        labelAccessorFn: (AlarmsStatistic datum, int? index) =>
            '${datum.alarmCount}',
        colorFn: (_, __) => charts.MaterialPalette.blue.shadeDefault,
        strokeWidthPxFn: (_, __) => 1.0,
      )..setAttribute(charts.rendererIdKey, 'default'),
    ];
  }

  /// 计算本周期比上周期的百分比变化
  /// 返回一个Map，key为名称，value为百分比变化
  Map<String, double> _calculatePercentageChanges(
      List<AlarmsStatistic> currentPeriod,
      List<AlarmsStatistic> previousPeriod) {
    Map<String, double> changes = {};

    // 将上周期数据转换为Map，便于查找
    Map<String, int> previousData = {};
    for (var item in previousPeriod) {
      if (item.name != null) {
        previousData[item.name!] = item.alarmCount;
      }
    }

    // 计算每个类别的百分比变化
    for (var current in currentPeriod) {
      if (current.name != null) {
        String name = current.name!;
        int currentCount = current.alarmCount;
        int previousCount = previousData[name] ?? 0;

        double percentage;
        if (previousCount == 0) {
          // 如果上周期为0，本周期有数据，则为100%增长
          percentage = currentCount > 0 ? 100.0 : 0.0;
        } else {
          // 计算百分比变化：(本周期 - 上周期) / 上周期 * 100
          percentage = ((currentCount - previousCount) / previousCount) * 100;
        }

        changes[name] = percentage;
      }
    }

    return changes;
  }

  /// 获取年月
  String _getYearMonth(AnalyzeModel detail) {
    String str = '';

    switch (detail.types) {
      case AnalyzeSortTypes.month:
        str = '${detail.yearMonthDay!.split('-')[1]}月份';

        break;
      case AnalyzeSortTypes.quarter:
        str =
            '(${detail.yearMonthDay!.split('-')[0]})第${controller.getQuarter(detail)}季度';

        break;
      case AnalyzeSortTypes.year:
        str = '${detail.yearMonthDay!.split('-')[0]}年';

        break;
      default:
    }

    return str;
  }
}
